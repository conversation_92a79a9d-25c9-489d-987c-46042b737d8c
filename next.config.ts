import type { NextConfig } from "next";
import createMDX from '@next/mdx';
// import remarkGfm from 'remark-gfm';
// import rehypePrettyCode from 'rehype-pretty-code';
import {
  transformerNotationDiff,
  transformerNotationHighlight,
  transformerNotationWordHighlight,
} from "@shikijs/transformers";
import { transformerFileName } from "./src/utils/transformers/fileName";
import remarkConditionalToc from "./src/utils/transformers/remark-conditional-toc";

const nextConfig: NextConfig = {
  // Rewrite blog posts assets url to the route.ts
  async rewrites() {
    return [
      {
        source: '/blog/posts/:slug*/assets/:path*',
        destination: '/blog/content/:slug*/assets/:path*',
      },
    ]
  }
};

const withMDX = createMDX({
  // Add markdown plugins here, as desired
  extension: /\.(md|mdx)$/,
  options: {
    remarkPlugins: [
      ['remark-frontmatter'],
      ['remark-gfm'], // Support github flavored markdown
      [remarkConditionalToc], // 自定义插件：条件性生成目录
      ['remark-toc', { maxDepth: 3, heading: 'Table of Contents' }],
      ['remark-mdx-frontmatter', { name: 'metadata' }],
    ],
    rehypePlugins: [
      [
        'rehype-pretty-code',
        {
          // theme: { light: 'github-light', dark: 'github-dark' },
          theme: "one-dark-pro",
          // theme: { light: 'vitesse-light', dark: 'vitesse-dark' },
          // theme: { light: "min-light", dark: "night-owl" },
          keepBackground: false,
          defaultColor: false,
          // wrap: false,
          transformers: [
            transformerNotationDiff({ matchAlgorithm: "v3" }),
            transformerNotationHighlight(),
            transformerNotationWordHighlight(),
            transformerFileName({ style: "v2", hideDot: false })
          ]
        },
      ],
      ['rehype-slug'],
      [
        'rehype-autolink-headings',
        {
          behavior: 'append',
          properties: {
            className: [
              'heading-link ms-2 no-underline opacity-75 md:opacity-0 md:group-hover:opacity-100 md:focus:opacity-100'
            ],
            ariaHidden: true,
            tabIndex: -1
          },
          content: {
            type: 'text',
            value: '#'
          }
        }
      ],
      ['rehype-katex', { strict: true, throwOnError: true }]
    ],
  },
});

export default withMDX(nextConfig);
