import { notFound, redirect } from 'next/navigation'
import { getBlogPosts } from '@/utils/blog'
import { baseUrl } from '@/utils/sitemap'

export default async function Blog({
  params,
}: {
  params: Promise<{ slugs: [string] }>
}) {
  const { slugs = [] } = await params
  const slug = slugs.join('/')

  const posts = getBlogPosts()
  const post = posts.find((p) => p.slug === slug)

  if (!post) {
    if (!slug.endsWith('/index')) {
      redirect(`/blog/posts/${slug}/index`)
    }
    notFound()
  }

  if (post.slug !== slug) {
    redirect(`/blog/posts/${post.slug}`)
  }

  const { default: Post } = await import(`@/content/${post.slug}.mdx`)
  return <Post />
}

// Dynamic metadata
export async function generateMetadata({ params }: { params: Promise<{ slugs: [string] }> }) {
  const { slugs = [] } = await params
  const post = getBlogPosts().find((post) => post.slug === slugs.join('/'))
  if (!post) {
    return
  }

  const {
    title,
    publishedAt: publishedTime,
    summary: description,
    image,
  } = post.metadata
  const ogImage = image
    ? image
    : `${baseUrl}/og?title=${encodeURIComponent(title)}`

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime,
      url: `${baseUrl}/blog/${post.slug}`,
      images: [
        {
          url: ogImage,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [ogImage],
    },
  }
}
 
export async function generateStaticParams() {
  const posts = getBlogPosts()

  return posts.map((post) => ({
    slugs: post.slug.split('/'),
  }))
}
 
export const dynamicParams = true
