// remark-conditional-toc.ts
// 根据 frontmatter 的 toc 参数控制是否生成目录

import type { Root } from 'mdast';
import type { VFile } from 'vfile';
import { visit } from 'unist-util-visit';

export default function remarkConditionalToc() {
  return function (tree: Root, file: VFile) {
    // 检查 frontmatter 中是否有 toc 参数
    const frontmatter = file.data?.frontmatter || {};
    const shouldGenerateToc = frontmatter.toc !== false; // 默认生成，除非明确设置为 false

    if (!shouldGenerateToc) {
      // 如果不需要生成目录，移除所有 toc 相关的节点
      visit(tree, (node, index, parent) => {
        if (
          node.type === 'mdxJsxFlowElement' &&
          'name' in node &&
          node.name === 'TableOfContents'
        ) {
          if (parent && typeof index === 'number') {
            parent.children.splice(index, 1);
            return [visit.SKIP, index];
          }
        }
      });
    }
  };
}