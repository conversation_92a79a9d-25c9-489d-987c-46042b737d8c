// remark-conditional-toc.js
// 根据 frontmatter 的 toc 参数控制是否生成目录

export default function remarkConditionalToc() {
  return function (tree, file) {
    // 检查 frontmatter 中是否有 toc 参数
    const frontmatter = file.data?.frontmatter || {};
    const shouldGenerateToc = frontmatter.toc !== false; // 默认生成，除非明确设置为 false
    
    if (!shouldGenerateToc) {
      // 如果不需要生成目录，移除所有 toc 相关的节点
      tree.children = tree.children.filter((node) => {
        if (node.type === 'mdxJsxFlowElement' && node.name === 'TableOfContents') {
          return false;
        }
        return true;
      });
    }
    return tree;
  };
} 